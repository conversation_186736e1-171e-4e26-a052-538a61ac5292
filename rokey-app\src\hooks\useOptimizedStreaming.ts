'use client';

import { useRef, useCallback } from 'react';
import { PlaygroundMessage } from '@/types/playground';

interface StreamingState {
  accumulatedText: string;
  messageId: string;
  isActive: boolean;
}

interface UseOptimizedStreamingOptions {
  updateInterval?: number; // How often to update UI (ms)
  batchSize?: number; // Minimum characters before updating
}

interface UseOptimizedStreamingReturn {
  startStreaming: (messageId: string) => void;
  appendText: (text: string) => void;
  finishStreaming: () => string;
  getCurrentText: () => string;
  isStreaming: boolean;
}

/**
 * Hook for optimized streaming that batches updates to reduce re-renders
 */
export function useOptimizedStreaming(
  onUpdate: (messageId: string, text: string) => void,
  options: UseOptimizedStreamingOptions = {}
): UseOptimizedStreamingReturn {
  const { updateInterval = 50, batchSize = 10 } = options;
  
  const streamingStateRef = useRef<StreamingState>({
    accumulatedText: '',
    messageId: '',
    isActive: false
  });
  
  const updateTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateRef = useRef<string>('');
  const pendingUpdateRef = useRef<boolean>(false);

  // Batched update function
  const flushUpdate = useCallback(() => {
    const state = streamingStateRef.current;
    if (state.isActive && state.accumulatedText !== lastUpdateRef.current) {
      onUpdate(state.messageId, state.accumulatedText);
      lastUpdateRef.current = state.accumulatedText;
      pendingUpdateRef.current = false;
    }
  }, [onUpdate]);

  // Schedule an update if not already pending
  const scheduleUpdate = useCallback(() => {
    if (!pendingUpdateRef.current) {
      pendingUpdateRef.current = true;
      
      // Clear existing timer
      if (updateTimerRef.current) {
        clearTimeout(updateTimerRef.current);
      }
      
      // Schedule batched update
      updateTimerRef.current = setTimeout(flushUpdate, updateInterval);
    }
  }, [flushUpdate, updateInterval]);

  const startStreaming = useCallback((messageId: string) => {
    streamingStateRef.current = {
      accumulatedText: '',
      messageId,
      isActive: true
    };
    lastUpdateRef.current = '';
    pendingUpdateRef.current = false;
    
    // Clear any existing timer
    if (updateTimerRef.current) {
      clearTimeout(updateTimerRef.current);
      updateTimerRef.current = null;
    }
  }, []);

  const appendText = useCallback((text: string) => {
    const state = streamingStateRef.current;
    if (!state.isActive) return;
    
    state.accumulatedText += text;
    
    // Schedule update if we've accumulated enough text or enough time has passed
    const textDelta = state.accumulatedText.length - lastUpdateRef.current.length;
    if (textDelta >= batchSize) {
      // Immediate update for large batches
      flushUpdate();
    } else {
      // Scheduled update for small increments
      scheduleUpdate();
    }
  }, [batchSize, flushUpdate, scheduleUpdate]);

  const finishStreaming = useCallback(() => {
    const state = streamingStateRef.current;
    if (!state.isActive) return '';
    
    // Clear any pending timer
    if (updateTimerRef.current) {
      clearTimeout(updateTimerRef.current);
      updateTimerRef.current = null;
    }
    
    // Final flush
    flushUpdate();
    
    const finalText = state.accumulatedText;
    state.isActive = false;
    
    return finalText;
  }, [flushUpdate]);

  const getCurrentText = useCallback(() => {
    return streamingStateRef.current.accumulatedText;
  }, []);

  const isStreaming = streamingStateRef.current.isActive;

  return {
    startStreaming,
    appendText,
    finishStreaming,
    getCurrentText,
    isStreaming
  };
}

/**
 * Hook for managing multiple streaming messages efficiently
 */
export function useMultiStreamingManager(
  onUpdate: (messageId: string, text: string) => void,
  options: UseOptimizedStreamingOptions = {}
) {
  const activeStreamsRef = useRef<Map<string, ReturnType<typeof useOptimizedStreaming>>>(new Map());
  
  const createStream = useCallback((messageId: string) => {
    // Clean up existing stream if any
    if (activeStreamsRef.current.has(messageId)) {
      const existingStream = activeStreamsRef.current.get(messageId)!;
      existingStream.finishStreaming();
    }
    
    // Create new stream (we can't use the hook here, so we'll implement the logic directly)
    const streamState = {
      accumulatedText: '',
      isActive: true,
      updateTimer: null as NodeJS.Timeout | null,
      lastUpdate: '',
      pendingUpdate: false
    };
    
    const flushUpdate = () => {
      if (streamState.isActive && streamState.accumulatedText !== streamState.lastUpdate) {
        onUpdate(messageId, streamState.accumulatedText);
        streamState.lastUpdate = streamState.accumulatedText;
        streamState.pendingUpdate = false;
      }
    };
    
    const scheduleUpdate = () => {
      if (!streamState.pendingUpdate) {
        streamState.pendingUpdate = true;
        if (streamState.updateTimer) {
          clearTimeout(streamState.updateTimer);
        }
        streamState.updateTimer = setTimeout(flushUpdate, options.updateInterval || 50);
      }
    };
    
    const stream = {
      appendText: (text: string) => {
        if (!streamState.isActive) return;
        streamState.accumulatedText += text;
        
        const textDelta = streamState.accumulatedText.length - streamState.lastUpdate.length;
        if (textDelta >= (options.batchSize || 10)) {
          flushUpdate();
        } else {
          scheduleUpdate();
        }
      },
      finishStreaming: () => {
        if (streamState.updateTimer) {
          clearTimeout(streamState.updateTimer);
        }
        flushUpdate();
        streamState.isActive = false;
        activeStreamsRef.current.delete(messageId);
        return streamState.accumulatedText;
      },
      getCurrentText: () => streamState.accumulatedText,
      isActive: () => streamState.isActive
    };
    
    activeStreamsRef.current.set(messageId, stream as any);
    return stream;
  }, [onUpdate, options.updateInterval, options.batchSize]);
  
  const getStream = useCallback((messageId: string) => {
    return activeStreamsRef.current.get(messageId);
  }, []);
  
  const finishAllStreams = useCallback(() => {
    const results: Record<string, string> = {};
    for (const [messageId, stream] of activeStreamsRef.current.entries()) {
      results[messageId] = stream.finishStreaming();
    }
    activeStreamsRef.current.clear();
    return results;
  }, []);
  
  return {
    createStream,
    getStream,
    finishAllStreams
  };
}
