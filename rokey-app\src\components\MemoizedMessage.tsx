'use client';

import React, { memo } from 'react';
import { CheckIcon, XCircleIcon, PencilSquareIcon } from '@heroicons/react/24/outline';
import LazyMarkdownRenderer from './LazyMarkdownRenderer';
import CopyButton from './CopyButton';
import RetryDropdown from './RetryDropdown';
import { PlaygroundMessage, PlaygroundMessageContentPartText, PlaygroundMessageContentPartImage } from '@/types/playground';

interface MemoizedMessageProps {
  message: PlaygroundMessage;
  index: number;
  isCanvasOpen: boolean;
  isCanvasMinimized: boolean;
  spacingClass: string;
  editingMessageId: string | null;
  editingContent: string;
  selectedConfigId: string | null;
  isLoading: boolean;
  onEditMessage: (messageId: string) => void;
  onSaveEditedMessage: () => void;
  onCancelEditingMessage: () => void;
  onRetryFromMessage: (messageId: string, apiKeyId?: string) => void;
  onEditingContentChange: (content: string) => void;
}

// Memoized message component to prevent unnecessary re-renders
const MemoizedMessage = memo(function MemoizedMessage({
  message,
  index,
  isCanvasOpen,
  isCanvasMinimized,
  spacingClass,
  editingMessageId,
  editingContent,
  selectedConfigId,
  isLoading,
  onEditMessage,
  onSaveEditedMessage,
  onCancelEditingMessage,
  onRetryFromMessage,
  onEditingContentChange
}: MemoizedMessageProps) {
  const isEditing = editingMessageId === message.id;

  return (
    <div
      key={message.id}
      data-message-id={message.id}
      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} group ${
        isCanvasOpen && !isCanvasMinimized ? '-ml-96' : ''
      } ${
        message.role === 'assistant' && isCanvasOpen && !isCanvasMinimized ? 'ml-8' : ''
      } ${spacingClass}`}
    >
      <div className={`${
        message.role === 'user'
          ? (isCanvasOpen && !isCanvasMinimized ? 'max-w-[60%]' : 'max-w-[50%]')
          : (isCanvasOpen && !isCanvasMinimized ? 'max-w-[100%]' : 'max-w-[100%]')
      } relative ${
        message.role === 'user'
          ? 'bg-gray-700/60 text-white rounded-2xl rounded-br-lg shadow-sm border border-gray-600/30'
          : message.role === 'assistant'
          ? 'text-white'
          : message.role === 'system'
          ? 'bg-amber-500/20 text-amber-300 rounded-xl border border-amber-500/30'
          : message.role === 'error'
          ? 'bg-red-500/20 text-red-300 rounded-xl border border-red-500/30'
          : 'text-white'
      } ${message.role === 'user' ? 'px-4 py-3' : message.role === 'system' || message.role === 'error' ? 'px-4 py-3' : ''}`}>
        
        {/* User message arrow */}
        {message.role === 'user' && (
          <div className="absolute -right-2 bottom-3 w-0 h-0 border-l-[12px] border-l-gray-700/60 border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent"></div>
        )}

        {/* Action buttons for user messages - positioned below the message bubble, show on hover */}
        {message.role === 'user' && !isEditing && (
          <div className="absolute -bottom-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <CopyButton
              text={message.content.filter(part => part.type === 'text').map(part => part.text).join('\n')}
              variant="message"
              size="sm"
              title="Copy message"
              className="text-gray-400 hover:text-white hover:bg-white/20 rounded-lg cursor-pointer"
            />
            <button
              onClick={() => onEditMessage(message.id)}
              className="p-1.5 text-gray-400 hover:text-white hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer"
              title="Edit message"
            >
              <PencilSquareIcon className="w-4 h-4 stroke-2" />
            </button>
          </div>
        )}

        {/* Copy and retry buttons for assistant/system/error messages - positioned below the message box on bottom left, always visible */}
        {message.role !== 'user' && !isEditing && (
          <div className="absolute -bottom-8 left-0 z-10 flex items-center space-x-2">
            <CopyButton
              text={message.content.filter(part => part.type === 'text').map(part => part.text).join('\n')}
              variant="message"
              size="sm"
              title="Copy message"
            />
            {message.role === 'assistant' && selectedConfigId && (
              <RetryDropdown
                configId={selectedConfigId}
                onRetry={(apiKeyId) => onRetryFromMessage(message.id, apiKeyId)}
                disabled={isLoading}
              />
            )}
          </div>
        )}

        {/* Message content */}
        {isEditing ? (
          /* Edit mode */
          <div className="space-y-3">
            <textarea
              value={editingContent}
              onChange={(e) => onEditingContentChange(e.target.value)}
              className="w-full h-32 bg-white/10 text-white rounded-lg px-3 py-2 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/50"
              placeholder="Edit your message..."
              autoFocus
            />
            <div className="flex space-x-2">
              <button
                onClick={onSaveEditedMessage}
                className="flex items-center space-x-1 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-all duration-200"
              >
                <CheckIcon className="w-4 h-4" />
                <span>Save & Continue</span>
              </button>
              <button
                onClick={onCancelEditingMessage}
                className="flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200"
              >
                <XCircleIcon className="w-4 h-4" />
                <span>Cancel</span>
              </button>
            </div>
            <p className="text-white/70 text-xs">
              💡 Saving will restart the conversation from this point, removing all messages that came after.
            </p>
          </div>
        ) : (
          /* Normal message display */
          message.content.map((part, partIndex) => {
            if (part.type === 'text') {
              // Use LazyMarkdownRenderer for assistant messages, plain text for others
              if (message.role === 'assistant') {
                return (
                  <LazyMarkdownRenderer
                    key={partIndex}
                    content={part.text}
                    className="text-sm"
                  />
                );
              } else {
                return (
                  <div key={partIndex} className="text-sm whitespace-pre-wrap">
                    {part.text}
                  </div>
                );
              }
            } else if (part.type === 'image_url') {
              return (
                <div key={partIndex} className="mt-2">
                  <img
                    src={part.image_url.url}
                    alt="User uploaded image"
                    className="max-w-full h-auto rounded-lg border border-gray-600/30"
                    style={{ maxHeight: '300px' }}
                  />
                </div>
              );
            }
            return null;
          })
        )}
      </div>
    </div>
  );
});

export default MemoizedMessage;
