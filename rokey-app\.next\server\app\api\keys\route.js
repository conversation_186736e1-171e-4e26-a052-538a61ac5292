/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/keys/route";
exports.ids = ["app/api/keys/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/keys/route.ts */ \"(rsc)/./src/app/api/keys/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/keys/route\",\n        pathname: \"/api/keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/keys/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\keys\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/keys/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/keys/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_encryption__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/encryption */ \"(rsc)/./src/lib/encryption.ts\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stripe-client */ \"(rsc)/./src/lib/stripe-client.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_4__);\n\n// cookies function is not directly needed here if createSupabaseServerClientOnRequest handles it\n\n // encrypt returns a single string now\n\n // Import Node.js crypto module\n// POST /api/keys\n// Adds a new API key to a specific custom_api_config\nasync function POST(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user (more secure than getSession)\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    if (authError || !user) {\n        console.error('Authentication error in POST /api/keys:', authError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to create API keys.'\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const keyData = await request.json();\n        const { custom_api_config_id, provider, predefined_model_id, api_key_raw, label, temperature = 1.0 } = keyData;\n        if (!custom_api_config_id || !provider || !predefined_model_id || !api_key_raw || !label) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields: custom_api_config_id, provider, predefined_model_id, api_key_raw, label'\n            }, {\n                status: 400\n            });\n        }\n        // Validate temperature\n        if (temperature < 0.0 || temperature > 2.0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Temperature must be between 0.0 and 2.0'\n            }, {\n                status: 400\n            });\n        }\n        if (typeof api_key_raw !== 'string' || api_key_raw.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'API key cannot be empty.'\n            }, {\n                status: 400\n            });\n        }\n        // Check user's subscription tier and API key limits for this specific config\n        const { data: subscription } = await supabase.from('subscriptions').select('tier').eq('user_id', user.id).eq('status', 'active').single();\n        const userTier = subscription?.tier || 'free';\n        // Count current API keys for this specific configuration\n        const { count: currentApiKeyCount } = await supabase.from('api_keys').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('custom_api_config_id', custom_api_config_id).eq('user_id', user.id);\n        // Check if user can create more API keys for this configuration\n        if (!(0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_3__.canPerformAction)(userTier, 'create_api_key', currentApiKeyCount || 0)) {\n            const tierConfig = (0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_3__.getTierConfig)(userTier);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `You have reached the maximum number of API keys (${tierConfig.limits.apiKeysPerConfig}) per configuration for your ${userTier} plan. Please upgrade to add more API keys.`\n            }, {\n                status: 403\n            });\n        }\n        // ROKEY_ENCRYPTION_KEY is checked within encryption.ts now, but good to be aware of its necessity.\n        // const encryptionKey = process.env.ROKEY_ENCRYPTION_KEY;\n        // if (!encryptionKey) {\n        //   console.error('ROKEY_ENCRYPTION_KEY is not set.');\n        //   return NextResponse.json({ error: 'Server configuration error: Encryption key not found.' }, { status: 500 });\n        // }\n        // encrypt function now returns a single string: iv:authTag:encryptedData\n        const encrypted_api_key_combined = await (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_2__.encrypt)(api_key_raw);\n        const api_key_hash = crypto__WEBPACK_IMPORTED_MODULE_4___default().createHash('sha256').update(api_key_raw).digest('hex');\n        // The type Omit<ApiKey, ...> will align because ApiKey no longer has 'iv'\n        const newDbKey = {\n            custom_api_config_id,\n            provider,\n            predefined_model_id,\n            encrypted_api_key: encrypted_api_key_combined,\n            label,\n            api_key_hash,\n            status: 'active',\n            is_default_general_chat_model: false,\n            temperature,\n            user_id: user.id\n        };\n        const { data, error } = await supabase.from('api_keys').insert(newDbKey).select().single();\n        if (error) {\n            console.error('Supabase error creating API key:', error);\n            if (error.code === '23503') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid custom_api_config_id or predefined_model_id.',\n                    details: error.message\n                }, {\n                    status: 400\n                });\n            }\n            if (error.code === '23505') {\n                // Check if this is the new unique_model_per_config constraint\n                if (error.message.includes('unique_model_per_config')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.',\n                        details: error.message\n                    }, {\n                        status: 409\n                    });\n                }\n                // Fallback for other unique constraint violations\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'A unique constraint was violated.',\n                    details: error.message\n                }, {\n                    status: 409\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to save API key',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // If this is the first key for the config, or no other key is default, make this one default.\n        if (data) {\n            const { data: existingDefaults, error: defaultCheckError } = await supabase.from('api_keys').select('id').eq('custom_api_config_id', custom_api_config_id).eq('user_id', user.id) // Filter by user_id for RLS compliance\n            .eq('is_default_general_chat_model', true).neq('id', data.id) // Exclude the newly added key itself from this check\n            .limit(1);\n            if (defaultCheckError) {\n                console.error('Error checking for existing default keys:', defaultCheckError);\n            // Proceed without making it default, but log the error. The key is still saved.\n            } else if (!existingDefaults || existingDefaults.length === 0) {\n                // No other key is default, so make this new one default\n                const { data: updatedKey, error: updateError } = await supabase.from('api_keys').update({\n                    is_default_general_chat_model: true\n                }).eq('id', data.id).eq('user_id', user.id) // Filter by user_id for RLS compliance\n                .select().single();\n                if (updateError) {\n                    console.error('Error updating new key to be default:', updateError);\n                // Key is saved, but failed to make it default. Log and proceed.\n                } else {\n                    // Successfully made the new key default, return this updated key data\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(updatedKey, {\n                        status: 201\n                    });\n                }\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 201\n        });\n    } catch (e) {\n        console.error('Error in POST /api/keys:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        // Catch errors from encrypt function (e.g., if ROKEY_ENCRYPTION_KEY was invalid)\n        if (e.message.includes('Invalid ROKEY_ENCRYPTION_KEY') || e.message.includes('Encryption input must be a non-empty string')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server-side encryption error',\n                details: e.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/keys?custom_config_id=<ID>\n// Retrieves all API keys for a specific custom_api_config_id\nasync function GET(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user (more secure than getSession)\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    if (authError || !user) {\n        console.error('Authentication error in GET /api/keys:', authError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to view API keys.'\n        }, {\n            status: 401\n        });\n    }\n    const { searchParams } = new URL(request.url);\n    const customConfigId = searchParams.get('custom_config_id');\n    if (!customConfigId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'custom_config_id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const { data: keys, error } = await supabase// Ensure selected fields match DisplayApiKey definition\n        .from('api_keys').select('id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at, is_default_general_chat_model').eq('custom_api_config_id', customConfigId).eq('user_id', user.id) // Filter by user_id for RLS compliance\n        .order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Supabase error fetching API keys:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch API keys',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        const displayKeys = (keys || []).map((key)=>({\n                id: key.id,\n                custom_api_config_id: key.custom_api_config_id,\n                provider: key.provider,\n                predefined_model_id: key.predefined_model_id,\n                label: key.label,\n                status: key.status,\n                temperature: key.temperature,\n                created_at: key.created_at,\n                last_used_at: key.last_used_at,\n                is_default_general_chat_model: key.is_default_general_chat_model\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(displayKeys, {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in GET /api/keys:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/keys?id=<ID>\n// Updates an existing API key (currently supports temperature updates)\nasync function PUT(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in PUT /api/keys:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to update API keys.'\n        }, {\n            status: 401\n        });\n    }\n    const user = session.user;\n    const { searchParams } = new URL(request.url);\n    const keyId = searchParams.get('id');\n    if (!keyId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const updateData = await request.json();\n        const { temperature, predefined_model_id } = updateData;\n        // Prepare update object\n        const updateFields = {};\n        if (temperature !== undefined) {\n            // Validate temperature\n            if (temperature < 0.0 || temperature > 2.0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Temperature must be between 0.0 and 2.0'\n                }, {\n                    status: 400\n                });\n            }\n            updateFields.temperature = temperature;\n        }\n        if (predefined_model_id !== undefined) {\n            if (typeof predefined_model_id !== 'string' || predefined_model_id.trim().length === 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Model ID must be a non-empty string'\n                }, {\n                    status: 400\n                });\n            }\n            updateFields.predefined_model_id = predefined_model_id;\n        }\n        // If no fields to update, return error\n        if (Object.keys(updateFields).length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No valid fields provided for update'\n            }, {\n                status: 400\n            });\n        }\n        const { data, error } = await supabase.from('api_keys').update(updateFields).eq('id', keyId).eq('user_id', user.id) // Filter by user_id for RLS compliance\n        .select('id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at, is_default_general_chat_model').single();\n        if (error) {\n            console.error('Supabase error updating API key:', error);\n            if (error.code === '23505' && error.message.includes('unique_model_per_config')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'This model is already configured in this setup. Each model can only be used once per configuration.',\n                    details: error.message\n                }, {\n                    status: 409\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to update API key',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in PUT /api/keys:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/keys/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/encryption.ts":
/*!*******************************!*\
  !*** ./src/lib/encryption.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n// Web Crypto API compatible encryption for Edge Runtime\nconst ALGORITHM = 'AES-GCM';\nconst IV_LENGTH = 12; // Recommended for GCM\n// Ensure your ROKEY_ENCRYPTION_KEY is a 64-character hex string (32 bytes)\nconst ROKEY_ENCRYPTION_KEY_FROM_ENV = process.env.ROKEY_ENCRYPTION_KEY;\nconsole.log('[DEBUG] ROKEY_ENCRYPTION_KEY from process.env:', ROKEY_ENCRYPTION_KEY_FROM_ENV);\nconsole.log('[DEBUG] Length:', ROKEY_ENCRYPTION_KEY_FROM_ENV?.length);\nif (!ROKEY_ENCRYPTION_KEY_FROM_ENV || ROKEY_ENCRYPTION_KEY_FROM_ENV.length !== 64) {\n    throw new Error('Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.');\n}\n// Convert hex string to Uint8Array for Web Crypto API\nfunction hexToUint8Array(hex) {\n    const bytes = new Uint8Array(hex.length / 2);\n    for(let i = 0; i < hex.length; i += 2){\n        bytes[i / 2] = parseInt(hex.substr(i, 2), 16);\n    }\n    return bytes;\n}\n// Convert Uint8Array to hex string\nfunction uint8ArrayToHex(bytes) {\n    return Array.from(bytes, (byte)=>byte.toString(16).padStart(2, '0')).join('');\n}\nconst keyBytes = hexToUint8Array(ROKEY_ENCRYPTION_KEY_FROM_ENV);\nasync function encrypt(text) {\n    if (typeof text !== 'string' || text.length === 0) {\n        throw new Error('Encryption input must be a non-empty string.');\n    }\n    // Generate random IV\n    const iv = crypto.getRandomValues(new Uint8Array(IV_LENGTH));\n    // Import the key for Web Crypto API\n    const cryptoKey = await crypto.subtle.importKey('raw', keyBytes, {\n        name: ALGORITHM\n    }, false, [\n        'encrypt'\n    ]);\n    // Encrypt the text\n    const encodedText = new TextEncoder().encode(text);\n    const encryptedBuffer = await crypto.subtle.encrypt({\n        name: ALGORITHM,\n        iv: iv\n    }, cryptoKey, encodedText);\n    const encryptedArray = new Uint8Array(encryptedBuffer);\n    // For AES-GCM, the auth tag is included in the encrypted data (last 16 bytes)\n    const encryptedData = encryptedArray.slice(0, -16);\n    const authTag = encryptedArray.slice(-16);\n    // Return IV:authTag:encryptedData format\n    return `${uint8ArrayToHex(iv)}:${uint8ArrayToHex(authTag)}:${uint8ArrayToHex(encryptedData)}`;\n}\nasync function decrypt(encryptedText) {\n    if (typeof encryptedText !== 'string' || encryptedText.length === 0) {\n        throw new Error('Decryption input must be a non-empty string.');\n    }\n    const parts = encryptedText.split(':');\n    if (parts.length !== 3) {\n        throw new Error('Invalid encrypted text format. Expected iv:authTag:encryptedData');\n    }\n    const iv = hexToUint8Array(parts[0]);\n    const authTag = hexToUint8Array(parts[1]);\n    const encryptedData = hexToUint8Array(parts[2]);\n    if (iv.length !== IV_LENGTH) {\n        throw new Error(`Invalid IV length. Expected ${IV_LENGTH} bytes.`);\n    }\n    if (authTag.length !== 16) {\n        throw new Error(`Invalid authTag length. Expected 16 bytes.`);\n    }\n    // Import the key for Web Crypto API\n    const cryptoKey = await crypto.subtle.importKey('raw', keyBytes, {\n        name: ALGORITHM\n    }, false, [\n        'decrypt'\n    ]);\n    // Combine encrypted data and auth tag for Web Crypto API\n    const combinedData = new Uint8Array(encryptedData.length + authTag.length);\n    combinedData.set(encryptedData);\n    combinedData.set(authTag, encryptedData.length);\n    // Decrypt the data\n    const decryptedBuffer = await crypto.subtle.decrypt({\n        name: ALGORITHM,\n        iv: iv\n    }, cryptoKey, combinedData);\n    return new TextDecoder().decode(decryptedBuffer);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/encryption.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-client.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-client.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TIER_CONFIGS: () => (/* binding */ TIER_CONFIGS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getPriceIdForTier: () => (/* binding */ getPriceIdForTier),\n/* harmony export */   getTierConfig: () => (/* binding */ getTierConfig),\n/* harmony export */   getTierFromPriceId: () => (/* binding */ getTierFromPriceId),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess)\n/* harmony export */ });\n/* harmony import */ var _stripe_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stripe-config */ \"(rsc)/./src/lib/stripe-config.ts\");\n// Client-safe Stripe utilities (no server-side Stripe instance)\n\nconst TIER_CONFIGS = {\n    free: {\n        name: 'Free',\n        price: '$0',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.FREE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.FREE,\n        features: [\n            'Unlimited API requests',\n            '1 Custom Configuration',\n            '3 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback routing only',\n            'Basic analytics only',\n            'No custom roles, basic router only',\n            'Limited logs',\n            'Community support'\n        ],\n        limits: {\n            configurations: 1,\n            apiKeysPerConfig: 3,\n            apiRequests: 999999,\n            canUseAdvancedRouting: false,\n            canUseCustomRoles: false,\n            maxCustomRoles: 0,\n            canUsePromptEngineering: false,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    starter: {\n        name: 'Starter',\n        price: '$20',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.STARTER,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.STARTER,\n        features: [\n            'Unlimited API requests',\n            '15 Custom Configurations',\n            '5 API Keys per config',\n            'All 300+ AI models',\n            'Intelligent routing strategies',\n            'Up to 3 custom roles',\n            'Intelligent role routing',\n            'Prompt engineering (no file upload)',\n            'Enhanced logs and analytics',\n            'Community support'\n        ],\n        limits: {\n            configurations: 15,\n            apiKeysPerConfig: 5,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 3,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    professional: {\n        name: 'Professional',\n        price: '$50',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.PROFESSIONAL,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.PROFESSIONAL,\n        features: [\n            'Unlimited API requests',\n            'Unlimited Custom Configurations',\n            'Unlimited API Keys per config',\n            'All 300+ AI models',\n            'All advanced routing strategies',\n            'Unlimited custom roles',\n            'Prompt engineering + Knowledge base (5 documents)',\n            'Semantic caching',\n            'Advanced analytics and logging',\n            'Priority email support'\n        ],\n        limits: {\n            configurations: 999999,\n            apiKeysPerConfig: 999999,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 5,\n            canUseSemanticCaching: true\n        }\n    }\n};\nfunction getTierConfig(tier) {\n    return TIER_CONFIGS[tier];\n}\nfunction getPriceIdForTier(tier) {\n    return TIER_CONFIGS[tier].priceId;\n}\nfunction getTierFromPriceId(priceId) {\n    for (const [tier, config] of Object.entries(TIER_CONFIGS)){\n        if (config.priceId === priceId) {\n            return tier;\n        }\n    }\n    return 'free'; // Default fallback to free tier\n}\nfunction formatPrice(tier) {\n    return TIER_CONFIGS[tier].price;\n}\nfunction canPerformAction(tier, action, currentCount) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(action){\n        case 'create_config':\n            return currentCount < limits.configurations;\n        case 'create_api_key':\n            return currentCount < limits.apiKeysPerConfig;\n        default:\n            return true;\n    }\n}\nfunction hasFeatureAccess(tier, feature) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(feature){\n        case 'custom_roles':\n            return limits.canUseCustomRoles;\n        case 'knowledge_base':\n            return limits.canUseKnowledgeBase;\n        case 'advanced_routing':\n            return limits.canUseAdvancedRouting;\n        case 'prompt_engineering':\n            return limits.canUsePromptEngineering;\n        case 'semantic_caching':\n            return limits.canUseSemanticCaching;\n        case 'configurations':\n            return limits.configurations > 0; // All tiers can create at least some configurations\n        default:\n            return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUBLIC_STRIPE_PRICE_IDS: () => (/* binding */ PUBLIC_STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_ENV_INFO: () => (/* binding */ STRIPE_ENV_INFO),\n/* harmony export */   STRIPE_KEYS: () => (/* binding */ STRIPE_KEYS),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_PRODUCT_IDS: () => (/* binding */ STRIPE_PRODUCT_IDS)\n/* harmony export */ });\n// Stripe Configuration with Environment Detection\n// Automatically switches between test and live keys based on environment\nconst isProduction = \"development\" === 'production';\n// Stripe Keys - Auto-selected based on environment\nconst STRIPE_KEYS = {\n    publishableKey: isProduction ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY : process.env.STRIPE_TEST_PUBLISHABLE_KEY,\n    secretKey: isProduction ? process.env.STRIPE_LIVE_SECRET_KEY : process.env.STRIPE_TEST_SECRET_KEY,\n    webhookSecret: isProduction ? process.env.STRIPE_LIVE_WEBHOOK_SECRET : process.env.STRIPE_TEST_WEBHOOK_SECRET\n};\n// Stripe Price IDs - Auto-selected based on environment\nconst STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Stripe Product IDs - Auto-selected based on environment\nconst STRIPE_PRODUCT_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID : process.env.STRIPE_TEST_FREE_PRODUCT_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID : process.env.STRIPE_TEST_STARTER_PRODUCT_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID\n};\n// Public Price IDs for frontend (auto-selected based on environment)\nconst PUBLIC_STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Environment info for debugging\nconst STRIPE_ENV_INFO = {\n    isProduction,\n    environment: isProduction ? 'LIVE' : 'TEST',\n    keysUsed: {\n        publishable: STRIPE_KEYS.publishableKey ? STRIPE_KEYS.publishableKey.substring(0, 20) + '...' : 'undefined',\n        secret: STRIPE_KEYS.secretKey ? STRIPE_KEYS.secretKey.substring(0, 20) + '...' : 'undefined',\n        webhook: STRIPE_KEYS.webhookSecret ? STRIPE_KEYS.webhookSecret.substring(0, 15) + '...' : 'undefined'\n    }\n};\n// Log environment info in development\nif (!isProduction) {\n    console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);\n    console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@supabase","vendor-chunks/next","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2Froute&page=%2Fapi%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();