"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx":
/*!***************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyCard.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyCard: () => (/* binding */ ApiKeyCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Globe,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Globe,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Globe,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Globe,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Globe,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Globe,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyCard auto */ \n\n\n\n\n\n\nfunction ApiKeyCard(param) {\n    let { apiKey, onRevoke } = param;\n    var _apiKey_allowed_ips, _apiKey_allowed_domains, _apiKey_allowed_ips1, _apiKey_allowed_domains1;\n    const copyToClipboard = async (text)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key copied to clipboard');\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to copy API key');\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'active':\n                return 'bg-green-900/30 text-green-300 border-green-500/50';\n            case 'inactive':\n                return 'bg-yellow-900/30 text-yellow-300 border-yellow-500/50';\n            case 'revoked':\n                return 'bg-red-900/30 text-red-300 border-red-500/50';\n            case 'expired':\n                return 'bg-gray-800/50 text-gray-300 border-gray-600/50';\n            default:\n                return 'bg-gray-800/50 text-gray-300 border-gray-600/50';\n        }\n    };\n    const isExpired = apiKey.expires_at && new Date(apiKey.expires_at) < new Date();\n    const isActive = apiKey.status === 'active' && !isExpired;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 transition-all duration-200 hover:border-gray-700/50 \".concat(!isActive ? 'opacity-75' : ''),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white\",\n                                    children: apiKey.key_name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        \"Configuration: \",\n                                        apiKey.custom_api_configs.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    className: getStatusColor(apiKey.status),\n                                    children: apiKey.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                isExpired && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    className: \"bg-red-900/30 text-red-300 border-red-500/50\",\n                                    children: \"Expired\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-300\",\n                                children: \"API Key (Masked)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 p-3 bg-gray-800/50 rounded-lg border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"flex-1 text-sm font-mono text-gray-300\",\n                                        children: [\n                                            apiKey.key_prefix,\n                                            \"_\",\n                                            '*'.repeat(28),\n                                            typeof apiKey.masked_key === 'string' ? apiKey.masked_key.slice(-4) : 'xxxx'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>copyToClipboard(\"\".concat(apiKey.key_prefix, \"_\").concat('*'.repeat(28)).concat(typeof apiKey.masked_key === 'string' ? apiKey.masked_key.slice(-4) : 'xxxx')),\n                                        className: \"h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-700/50\",\n                                        title: \"Copy masked key (for reference only)\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-xs text-amber-400 bg-amber-900/20 p-2 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"⚠️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Full API key was only shown once during creation for security. Save it securely when creating new keys.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-300\",\n                                children: \"Permissions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    apiKey.permissions.chat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"text-xs bg-blue-900/30 text-blue-300 border-blue-500/50\",\n                                        children: \"Chat Completions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    apiKey.permissions.streaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"text-xs bg-green-900/30 text-green-300 border-green-500/50\",\n                                        children: \"Streaming\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    apiKey.permissions.all_models && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"text-xs bg-purple-900/30 text-purple-300 border-purple-500/50\",\n                                        children: \"All Models\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    ((((_apiKey_allowed_ips = apiKey.allowed_ips) === null || _apiKey_allowed_ips === void 0 ? void 0 : _apiKey_allowed_ips.length) || 0) > 0 || (((_apiKey_allowed_domains = apiKey.allowed_domains) === null || _apiKey_allowed_domains === void 0 ? void 0 : _apiKey_allowed_domains.length) || 0) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-300 flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Security Restrictions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1 text-xs\",\n                                children: [\n                                    (((_apiKey_allowed_ips1 = apiKey.allowed_ips) === null || _apiKey_allowed_ips1 === void 0 ? void 0 : _apiKey_allowed_ips1.length) || 0) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"IPs:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: apiKey.allowed_ips.join(', ')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this),\n                                    (((_apiKey_allowed_domains1 = apiKey.allowed_domains) === null || _apiKey_allowed_domains1 === void 0 ? void 0 : _apiKey_allowed_domains1.length) || 0) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Domains:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: apiKey.allowed_domains.join(', ')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-300 flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Usage Statistics\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Total Requests:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 font-semibold text-white\",\n                                                children: apiKey.total_requests.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    apiKey.last_used_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Last Used:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 font-semibold text-white\",\n                                                children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_9__.formatDistanceToNow)(new Date(apiKey.last_used_at), {\n                                                    addSuffix: true\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    apiKey.expires_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-300 flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Expiration\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold \".concat(isExpired ? 'text-red-400' : 'text-white'),\n                                        children: [\n                                            new Date(apiKey.expires_at).toLocaleDateString(),\n                                            \" at\",\n                                            ' ',\n                                            new Date(apiKey.expires_at).toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    !isExpired && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-gray-400\",\n                                        children: [\n                                            \"(\",\n                                            (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_9__.formatDistanceToNow)(new Date(apiKey.expires_at), {\n                                                addSuffix: true\n                                            }),\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-end pt-2 border-t border-gray-700\",\n                        children: apiKey.status !== 'revoked' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"destructive\",\n                            size: \"sm\",\n                            onClick: ()=>onRevoke(apiKey.id),\n                            className: \"text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-3 w-3 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this),\n                                \"Revoke\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_c = ApiKeyCard;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\n"));

/***/ })

});