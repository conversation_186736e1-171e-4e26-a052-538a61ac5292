"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx":
/*!******************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyManager.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyManager: () => (/* binding */ ApiKeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _ApiKeyCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ApiKeyCard */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\");\n/* harmony import */ var _CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CreateApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\");\n/* harmony import */ var _components_TierEnforcement_FreeTierMarketingBanner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/TierEnforcement/FreeTierMarketingBanner */ \"(app-pages-browser)/./src/components/TierEnforcement/FreeTierMarketingBanner.tsx\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ApiKeyManager(param) {\n    let { configId, configName } = param;\n    _s();\n    const [apiKeys, setApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subscriptionInfo, setSubscriptionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_8__.useConfirmation)();\n    // Fetch API keys for this configuration\n    const fetchApiKeys = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/user-api-keys?config_id=\".concat(configId));\n            if (!response.ok) {\n                throw new Error('Failed to fetch API keys');\n            }\n            const data = await response.json();\n            setApiKeys(data.api_keys || []);\n        } catch (error) {\n            console.error('Error fetching API keys:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to load API keys');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch subscription info\n    const fetchSubscriptionInfo = async (currentKeyCount)=>{\n        try {\n            // Get the tier from the user's active subscription\n            const tierResponse = await fetch('/api/user/subscription-tier');\n            const tierData = tierResponse.ok ? await tierResponse.json() : null;\n            const tier = (tierData === null || tierData === void 0 ? void 0 : tierData.tier) || 'starter';\n            const limits = {\n                free: 3,\n                starter: 50,\n                professional: 999999,\n                enterprise: 999999\n            };\n            // Use provided count or current apiKeys length\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier,\n                keyLimit: limits[tier] || limits.free,\n                currentCount: keyCount\n            });\n        } catch (error) {\n            console.error('Error fetching subscription info:', error);\n            // Fallback to free tier\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier: 'free',\n                keyLimit: 3,\n                currentCount: keyCount\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            fetchApiKeys();\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        configId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            if (apiKeys.length >= 0) {\n                fetchSubscriptionInfo();\n            }\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        apiKeys\n    ]);\n    const handleCreateApiKey = async (keyData)=>{\n        try {\n            setCreating(true);\n            const response = await fetch('/api/user-api-keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...keyData,\n                    custom_api_config_id: configId\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to create API key');\n            }\n            const newApiKey = await response.json();\n            // Show the full API key in a special dialog since it's only shown once\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('API key created successfully!');\n            // Add the new key to the list with proper structure\n            const newKeyForDisplay = {\n                ...newApiKey,\n                // Add any missing fields that might be needed for display\n                custom_api_configs: {\n                    id: configId,\n                    name: configName\n                },\n                // Ensure we have a masked_key for display\n                masked_key: newApiKey.api_key ? newApiKey.api_key.slice(-4) : 'xxxx',\n                // Ensure arrays exist to prevent undefined errors\n                allowed_ips: newApiKey.allowed_ips || [],\n                allowed_domains: newApiKey.allowed_domains || [],\n                // Ensure other required fields exist\n                total_requests: newApiKey.total_requests || 0,\n                rate_limit_per_minute: newApiKey.rate_limit_per_minute || 0,\n                rate_limit_per_hour: newApiKey.rate_limit_per_hour || 0,\n                rate_limit_per_day: newApiKey.rate_limit_per_day || 0\n            };\n            setApiKeys((prevKeys)=>{\n                const newKeys = [\n                    newKeyForDisplay,\n                    ...prevKeys\n                ];\n                // Update subscription info with new count\n                fetchSubscriptionInfo(newKeys.length);\n                return newKeys;\n            });\n            // Don't refresh immediately to avoid closing the success dialog\n            // The user will see the new key in the list when they close the dialog\n            return newApiKey;\n        } catch (error) {\n            console.error('Error creating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(error.message || 'Failed to create API key');\n            throw error;\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleRevokeApiKey = async (keyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === keyId);\n        const keyName = (apiKey === null || apiKey === void 0 ? void 0 : apiKey.key_name) || 'this API key';\n        confirmation.showConfirmation({\n            title: 'Revoke API Key',\n            message: 'Are you sure you want to revoke \"'.concat(keyName, '\"? This action cannot be undone and will immediately disable the key.'),\n            confirmText: 'Revoke Key',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            try {\n                const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                    method: 'DELETE'\n                });\n                if (!response.ok) {\n                    const error = await response.json();\n                    throw new Error(error.error || 'Failed to revoke API key');\n                }\n                // Update the key status to revoked immediately after successful API call\n                setApiKeys((prevKeys)=>prevKeys.map((key)=>key.id === keyId ? {\n                            ...key,\n                            status: 'revoked'\n                        } : key));\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('API key revoked successfully');\n            } catch (error) {\n                console.error('Error revoking API key:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(error.message || 'Failed to revoke API key');\n                throw error; // Re-throw to let the confirmation modal handle the error state\n            }\n        });\n    };\n    const handleCreateDialogClose = (open)=>{\n        setShowCreateDialog(open);\n        // If dialog is being closed, refresh the API keys to ensure we have the latest data\n        if (!open) {\n            fetchApiKeys();\n        }\n    };\n    const canCreateMoreKeys = subscriptionInfo ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit : true;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin mr-2 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading API keys...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold flex items-center gap-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"API Keys\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mt-1\",\n                                children: [\n                                    \"Generate API keys for programmatic access to \",\n                                    configName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            \"Create API Key\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-end gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                disabled: true,\n                                className: \"flex items-center gap-2 opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Create API Key\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-orange-400 font-medium\",\n                                children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan for more API keys' : 'API key limit reached - upgrade for unlimited keys'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement_FreeTierMarketingBanner__WEBPACK_IMPORTED_MODULE_6__.FreeTierMarketingBanner, {\n                message: \"Unlock intelligent routing and more API keys\",\n                variant: \"compact\",\n                className: \"mb-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement_FreeTierMarketingBanner__WEBPACK_IMPORTED_MODULE_6__.StarterTierHint, {\n                className: \"mb-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            apiKeys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2 text-white\",\n                            children: \"No API Keys\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-4\",\n                            children: \"Create your first API key to start using the RouKey API programmatically.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this),\n                        canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setShowCreateDialog(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 17\n                                }, this),\n                                \"Create Your First API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    disabled: true,\n                                    className: \"opacity-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Create Your First API Key\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-orange-400 font-medium\",\n                                    children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan to create API keys' : 'API key limit reached - upgrade for unlimited keys'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: apiKeys.map((apiKey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyCard__WEBPACK_IMPORTED_MODULE_4__.ApiKeyCard, {\n                        apiKey: apiKey,\n                        onRevoke: handleRevokeApiKey\n                    }, apiKey.id, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 315,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_5__.CreateApiKeyDialog, {\n                open: showCreateDialog,\n                onOpenChange: handleCreateDialogClose,\n                onCreateApiKey: handleCreateApiKey,\n                configName: configName,\n                creating: creating,\n                subscriptionTier: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) || 'starter'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n        lineNumber: 231,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyManager, \"wzx+xNoPucKu2oL+bFaGPsR3hPI=\", false, function() {\n    return [\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_8__.useConfirmation\n    ];\n});\n_c = ApiKeyManager;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\n"));

/***/ })

});