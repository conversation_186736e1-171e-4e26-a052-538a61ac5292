'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Key,
  AlertTriangle,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react';
import { toast } from 'sonner';

interface CreateApiKeyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateApiKey: (keyData: any) => Promise<any>;
  configName: string;
  creating: boolean;
  subscriptionTier: string;
}

export function CreateApiKeyDialog({
  open,
  onOpenChange,
  onCreateApiKey,
  configName,
  creating,
  subscriptionTier
}: CreateApiKeyDialogProps) {
  const [step, setStep] = useState<'form' | 'success'>('form');
  const [createdApiKey, setCreatedApiKey] = useState<any>(null);
  const [showFullKey, setShowFullKey] = useState(true); // Show full key by default in success step
  const [copied, setCopied] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState({
    key_name: '',
    expires_at: ''
  });

  // Reset dialog state when it opens
  useEffect(() => {
    if (open) {
      setStep('form');
      setCreatedApiKey(null);
      setShowFullKey(true);
      setCopied(false);
      setFormData({
        key_name: '',
        expires_at: ''
      });
    }
  }, [open]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.key_name.trim()) {
      toast.error('Please enter a name for your API key');
      return;
    }

    try {
      const result = await onCreateApiKey({
        key_name: formData.key_name.trim(),
        expires_at: formData.expires_at || undefined
      });

      console.log('[CreateApiKeyDialog] API key creation result:', result);
      setCreatedApiKey(result);
      setStep('success');
      console.log('[CreateApiKeyDialog] Switched to success step');
    } catch (error) {
      console.error('[CreateApiKeyDialog] Error creating API key:', error);
      // Error is handled in the parent component
    }
  };



  const copyApiKey = async () => {
    if (createdApiKey?.api_key) {
      try {
        await navigator.clipboard.writeText(createdApiKey.api_key);
        setCopied(true);
        toast.success('API key copied to clipboard');
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        toast.error('Failed to copy API key');
      }
    }
  };

  const handleClose = () => {
    // Only allow closing if we're in the form step or if user explicitly closes from success step
    if (step === 'form') {
      // Reset all state
      setStep('form');
      setCreatedApiKey(null);
      setShowFullKey(true);
      setFormData({
        key_name: '',
        expires_at: ''
      });
      // Close the dialog
      onOpenChange(false);
    }
  };

  const handleSuccessClose = () => {
    // Reset all state
    setStep('form');
    setCreatedApiKey(null);
    setShowFullKey(true);
    setCopied(false);
    setFormData({
      key_name: '',
      expires_at: ''
    });
    // Close the dialog
    onOpenChange(false);
  };

  if (step === 'success' && createdApiKey) {
    console.log('[CreateApiKeyDialog] Rendering success step with API key:', createdApiKey.api_key?.substring(0, 20) + '...');
    return (
      <Dialog open={open} onOpenChange={() => {}} modal={true}>
        <DialogContent className="max-w-lg">
          <DialogHeader className="text-center space-y-3">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <Key className="h-8 w-8 text-green-600" />
            </div>
            <DialogTitle className="text-2xl font-bold text-gray-900">
              API Key Created Successfully!
            </DialogTitle>
            <DialogDescription className="text-gray-600">
              Save your API key now - this is the only time you'll see it in full.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Warning Alert */}
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800 font-medium">
                <strong>Important:</strong> This is the only time you'll see the full API key.
                Make sure to copy and store it securely.
              </AlertDescription>
            </Alert>

            {/* API Key Display */}
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700">
                Your API Key
              </Label>
              <div className="relative">
                <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
                  <code className="flex-1 text-sm font-mono text-gray-900 break-all select-all">
                    {showFullKey
                      ? createdApiKey.api_key
                      : `${createdApiKey.key_prefix}_${'*'.repeat(28)}${createdApiKey.api_key.slice(-4)}`
                    }
                  </code>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowFullKey(!showFullKey)}
                      className="h-8 w-8 p-0"
                      title={showFullKey ? "Hide key" : "Show key"}
                    >
                      {showFullKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={copyApiKey}
                      className={`h-8 w-8 p-0 ${copied ? 'text-green-600' : ''}`}
                      title="Copy to clipboard"
                    >
                      {copied ? <span className="text-xs">✓</span> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>
              <Button
                onClick={copyApiKey}
                variant="outline"
                className="w-full"
                disabled={copied}
              >
                {copied ? (
                  <>
                    <span className="text-green-600 mr-2">✓</span>
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy API Key
                  </>
                )}
              </Button>
            </div>

            {/* Key Details */}
            <div className="grid grid-cols-2 gap-4 text-sm bg-gray-50 p-4 rounded-lg">
              <div>
                <Label className="text-gray-600">Key Name</Label>
                <p className="font-medium text-gray-900">{createdApiKey.key_name}</p>
              </div>
              <div>
                <Label className="text-gray-600">Created</Label>
                <p className="font-medium text-gray-900">{new Date(createdApiKey.created_at).toLocaleString()}</p>
              </div>
            </div>
          </div>

          <DialogFooter className="pt-6">
            <Button
              onClick={handleSuccessClose}
              className="w-full"
            >
              I've Saved My API Key
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Create API Key
          </DialogTitle>
          <DialogDescription>
            Create a new API key for programmatic access to {configName}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="key_name">API Key Name *</Label>
              <Input
                id="key_name"
                value={formData.key_name}
                onChange={(e) => setFormData(prev => ({ ...prev, key_name: e.target.value }))}
                placeholder="e.g., Production API Key"
                required
                className="!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"
              />
              <p className="text-xs text-gray-600">
                A descriptive name to help you identify this API key
              </p>
            </div>
          </div>



          {/* Expiration */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="expires_at">Expiration Date (Optional)</Label>
              <Input
                id="expires_at"
                type="datetime-local"
                value={formData.expires_at}
                onChange={(e) => setFormData(prev => ({ ...prev, expires_at: e.target.value }))}
                min={new Date().toISOString().slice(0, 16)}
                className="!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"
              />
              <p className="text-xs text-gray-600">
                Leave empty for no expiration
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating ? 'Creating...' : 'Create API Key'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
