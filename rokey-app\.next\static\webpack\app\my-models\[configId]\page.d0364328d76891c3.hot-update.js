"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx":
/*!***************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyCard.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyCard: () => (/* binding */ ApiKeyCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Globe,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Globe,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Globe,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Globe,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Globe,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calendar,Copy,Globe,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyCard auto */ \n\n\n\n\n\n\nfunction ApiKeyCard(param) {\n    let { apiKey, onRevoke } = param;\n    var _apiKey_allowed_ips, _apiKey_allowed_domains, _apiKey_allowed_ips1, _apiKey_allowed_domains1;\n    const copyToClipboard = async (text)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('API key copied to clipboard');\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to copy API key');\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'active':\n                return 'bg-green-900/30 text-green-300 border-green-500/50';\n            case 'inactive':\n                return 'bg-yellow-900/30 text-yellow-300 border-yellow-500/50';\n            case 'revoked':\n                return 'bg-red-900/30 text-red-300 border-red-500/50';\n            case 'expired':\n                return 'bg-gray-800/50 text-gray-300 border-gray-600/50';\n            default:\n                return 'bg-gray-800/50 text-gray-300 border-gray-600/50';\n        }\n    };\n    const isExpired = apiKey.expires_at && new Date(apiKey.expires_at) < new Date();\n    const isActive = apiKey.status === 'active' && !isExpired;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 transition-all duration-200 hover:border-gray-700/50 \".concat(!isActive ? 'opacity-75' : ''),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white\",\n                                    children: apiKey.key_name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        \"Configuration: \",\n                                        apiKey.custom_api_configs.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    className: getStatusColor(apiKey.status),\n                                    children: apiKey.status\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                isExpired && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    className: \"bg-red-900/30 text-red-300 border-red-500/50\",\n                                    children: \"Expired\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-300\",\n                                children: \"API Key (Masked)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 p-3 bg-gray-800/50 rounded-lg border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"flex-1 text-sm font-mono text-gray-300\",\n                                        children: [\n                                            apiKey.key_prefix,\n                                            \"_\",\n                                            '*'.repeat(28),\n                                            typeof apiKey.masked_key === 'string' ? apiKey.masked_key.slice(-4) : 'xxxx'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>copyToClipboard(\"\".concat(apiKey.key_prefix, \"_\").concat('*'.repeat(28)).concat(typeof apiKey.masked_key === 'string' ? apiKey.masked_key.slice(-4) : 'xxxx')),\n                                        className: \"h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-700/50\",\n                                        title: \"Copy masked key (for reference only)\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-xs text-amber-400 bg-amber-900/20 p-2 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"⚠️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Full API key was only shown once during creation for security. Save it securely when creating new keys.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-300\",\n                                children: \"Permissions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    apiKey.permissions.chat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"text-xs bg-blue-900/30 text-blue-300 border-blue-500/50\",\n                                        children: \"Chat Completions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    apiKey.permissions.streaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"text-xs bg-green-900/30 text-green-300 border-green-500/50\",\n                                        children: \"Streaming\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    apiKey.permissions.all_models && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"text-xs bg-purple-900/30 text-purple-300 border-purple-500/50\",\n                                        children: \"All Models\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    ((((_apiKey_allowed_ips = apiKey.allowed_ips) === null || _apiKey_allowed_ips === void 0 ? void 0 : _apiKey_allowed_ips.length) || 0) > 0 || (((_apiKey_allowed_domains = apiKey.allowed_domains) === null || _apiKey_allowed_domains === void 0 ? void 0 : _apiKey_allowed_domains.length) || 0) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-300 flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Security Restrictions\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1 text-xs\",\n                                children: [\n                                    (((_apiKey_allowed_ips1 = apiKey.allowed_ips) === null || _apiKey_allowed_ips1 === void 0 ? void 0 : _apiKey_allowed_ips1.length) || 0) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"IPs:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: apiKey.allowed_ips.join(', ')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this),\n                                    (((_apiKey_allowed_domains1 = apiKey.allowed_domains) === null || _apiKey_allowed_domains1 === void 0 ? void 0 : _apiKey_allowed_domains1.length) || 0) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Domains:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: apiKey.allowed_domains.join(', ')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-300 flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Usage Statistics\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Total Requests:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 font-semibold text-white\",\n                                                children: (apiKey.total_requests || 0).toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    apiKey.last_used_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"Last Used:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 font-semibold text-white\",\n                                                children: (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_9__.formatDistanceToNow)(new Date(apiKey.last_used_at), {\n                                                    addSuffix: true\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    apiKey.expires_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-300 flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Expiration\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold \".concat(isExpired ? 'text-red-400' : 'text-white'),\n                                        children: [\n                                            new Date(apiKey.expires_at).toLocaleDateString(),\n                                            \" at\",\n                                            ' ',\n                                            new Date(apiKey.expires_at).toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    !isExpired && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-gray-400\",\n                                        children: [\n                                            \"(\",\n                                            (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_9__.formatDistanceToNow)(new Date(apiKey.expires_at), {\n                                                addSuffix: true\n                                            }),\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-end pt-2 border-t border-gray-700\",\n                        children: apiKey.status !== 'revoked' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"destructive\",\n                            size: \"sm\",\n                            onClick: ()=>onRevoke(apiKey.id),\n                            className: \"text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calendar_Copy_Globe_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-3 w-3 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this),\n                                \"Revoke\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyCard.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_c = ApiKeyCard;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\n"));

/***/ })

});