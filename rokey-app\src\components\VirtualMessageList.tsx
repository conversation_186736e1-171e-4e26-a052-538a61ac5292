'use client';

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import MemoizedMessage from './MemoizedMessage';
import { PlaygroundMessage } from '@/types/playground';

interface VirtualMessageListProps {
  messages: PlaygroundMessage[];
  isCanvasOpen: boolean;
  isCanvasMinimized: boolean;
  editingMessageId: string | null;
  editingContent: string;
  selectedConfigId: string | null;
  isLoading: boolean;
  onEditMessage: (messageId: string) => void;
  onSaveEditedMessage: () => void;
  onCancelEditingMessage: () => void;
  onRetryFromMessage: (messageId: string, apiKeyId?: string) => void;
  onEditingContentChange: (content: string) => void;
  className?: string;
}

interface VirtualItem {
  index: number;
  message: PlaygroundMessage;
  height: number;
  offset: number;
}

// Constants for virtual scrolling
const ESTIMATED_MESSAGE_HEIGHT = 120; // Average height per message
const BUFFER_SIZE = 5; // Number of items to render outside visible area
const OVERSCAN = 3; // Additional items for smooth scrolling

export const VirtualMessageList: React.FC<VirtualMessageListProps> = ({
  messages,
  isCanvasOpen,
  isCanvasMinimized,
  editingMessageId,
  editingContent,
  selectedConfigId,
  isLoading,
  onEditMessage,
  onSaveEditedMessage,
  onCancelEditingMessage,
  onRetryFromMessage,
  onEditingContentChange,
  className = ''
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  // Calculate virtual items with dynamic heights
  const virtualItems = useMemo((): VirtualItem[] => {
    let currentOffset = 0;
    return messages.map((message, index) => {
      // Estimate height based on message content
      let estimatedHeight = ESTIMATED_MESSAGE_HEIGHT;
      
      // Adjust height based on content length
      const textContent = message.content
        .filter(part => part.type === 'text')
        .map(part => part.text)
        .join('');
      
      // Rough estimation: 20 pixels per line, ~80 characters per line
      const estimatedLines = Math.max(1, Math.ceil(textContent.length / 80));
      estimatedHeight = Math.max(ESTIMATED_MESSAGE_HEIGHT, estimatedLines * 20 + 80);
      
      // Add extra height for user messages (they have background)
      if (message.role === 'user') {
        estimatedHeight += 20;
      }
      
      // Add extra height for editing mode
      if (editingMessageId === message.id) {
        estimatedHeight += 150;
      }

      const item: VirtualItem = {
        index,
        message,
        height: estimatedHeight,
        offset: currentOffset
      };

      currentOffset += estimatedHeight;
      return item;
    });
  }, [messages, editingMessageId]);

  const totalHeight = virtualItems.length > 0 ? virtualItems[virtualItems.length - 1].offset + virtualItems[virtualItems.length - 1].height : 0;

  // Calculate visible range
  const visibleRange = useMemo(() => {
    if (containerHeight === 0 || virtualItems.length === 0) {
      return { start: 0, end: Math.min(10, virtualItems.length) }; // Show first 10 items by default
    }

    // Find first visible item
    let start = 0;
    for (let i = 0; i < virtualItems.length; i++) {
      if (virtualItems[i].offset + virtualItems[i].height > scrollTop) {
        start = Math.max(0, i - BUFFER_SIZE);
        break;
      }
    }

    // Find last visible item
    let end = virtualItems.length;
    for (let i = start; i < virtualItems.length; i++) {
      if (virtualItems[i].offset > scrollTop + containerHeight) {
        end = Math.min(virtualItems.length, i + BUFFER_SIZE + OVERSCAN);
        break;
      }
    }

    return { start, end };
  }, [scrollTop, containerHeight, virtualItems]);

  // Get visible items
  const visibleItems = useMemo(() => {
    return virtualItems.slice(visibleRange.start, visibleRange.end);
  }, [virtualItems, visibleRange]);

  // Handle scroll events
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const target = e.currentTarget;
    setScrollTop(target.scrollTop);
  }, []);

  // Update container height on resize
  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        setContainerHeight(containerRef.current.clientHeight);
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollElementRef.current && messages.length > 0) {
      const isNearBottom = scrollTop + containerHeight >= totalHeight - 100;
      if (isNearBottom) {
        scrollElementRef.current.scrollTop = totalHeight;
      }
    }
  }, [messages.length, totalHeight, scrollTop, containerHeight]);

  // Determine spacing for each message
  const getSpacingClass = useCallback((item: VirtualItem) => {
    const prevItem = item.index > 0 ? virtualItems[item.index - 1] : null;
    const isResponseToUser = prevItem && prevItem.message.role === 'user' && item.message.role === 'assistant';
    return item.index === 0 ? 'pt-3' : isResponseToUser ? 'mt-6' : 'mt-32';
  }, [virtualItems]);

  return (
    <div ref={containerRef} className={`flex-1 overflow-hidden ${className}`}>
      <div
        ref={scrollElementRef}
        className="h-full overflow-y-auto"
        onScroll={handleScroll}
        style={{ scrollBehavior: 'smooth' }}
      >
        <div style={{ height: totalHeight, position: 'relative' }}>
          {visibleItems.map((item) => (
            <div
              key={item.message.id}
              style={{
                position: 'absolute',
                top: item.offset,
                left: 0,
                right: 0,
                minHeight: item.height,
                padding: '0 24px'
              }}
            >
              <MemoizedMessage
                message={item.message}
                index={item.index}
                isCanvasOpen={isCanvasOpen}
                isCanvasMinimized={isCanvasMinimized}
                spacingClass={getSpacingClass(item)}
                editingMessageId={editingMessageId}
                editingContent={editingContent}
                selectedConfigId={selectedConfigId}
                isLoading={isLoading}
                onEditMessage={onEditMessage}
                onSaveEditedMessage={onSaveEditedMessage}
                onCancelEditingMessage={onCancelEditingMessage}
                onRetryFromMessage={onRetryFromMessage}
                onEditingContentChange={onEditingContentChange}
              />
            </div>
          ))}
        </div>
      </div>
      
      {/* Performance indicator */}
      {messages.length > 50 && (
        <div className="absolute top-4 right-4 bg-black/50 text-white text-xs px-2 py-1 rounded">
          Virtual: {visibleItems.length}/{messages.length} messages
        </div>
      )}
    </div>
  );
};
