"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx":
/*!******************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyManager.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyManager: () => (/* binding */ ApiKeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _ApiKeyCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ApiKeyCard */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\");\n/* harmony import */ var _CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CreateApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\");\n/* harmony import */ var _components_TierEnforcement_FreeTierMarketingBanner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/TierEnforcement/FreeTierMarketingBanner */ \"(app-pages-browser)/./src/components/TierEnforcement/FreeTierMarketingBanner.tsx\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ApiKeyManager(param) {\n    let { configId, configName } = param;\n    _s();\n    const [apiKeys, setApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subscriptionInfo, setSubscriptionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_8__.useConfirmation)();\n    // Fetch API keys for this configuration\n    const fetchApiKeys = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/user-api-keys?config_id=\".concat(configId));\n            if (!response.ok) {\n                throw new Error('Failed to fetch API keys');\n            }\n            const data = await response.json();\n            setApiKeys(data.api_keys || []);\n        } catch (error) {\n            console.error('Error fetching API keys:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to load API keys');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch subscription info\n    const fetchSubscriptionInfo = async (currentKeyCount)=>{\n        try {\n            // Get the tier from the user's active subscription\n            const tierResponse = await fetch('/api/user/subscription-tier');\n            const tierData = tierResponse.ok ? await tierResponse.json() : null;\n            const tier = (tierData === null || tierData === void 0 ? void 0 : tierData.tier) || 'starter';\n            const limits = {\n                free: 3,\n                starter: 50,\n                professional: 999999,\n                enterprise: 999999\n            };\n            // Use provided count or current apiKeys length\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier,\n                keyLimit: limits[tier] || limits.free,\n                currentCount: keyCount\n            });\n        } catch (error) {\n            console.error('Error fetching subscription info:', error);\n            // Fallback to free tier\n            const keyCount = currentKeyCount !== undefined ? currentKeyCount : apiKeys.length;\n            setSubscriptionInfo({\n                tier: 'free',\n                keyLimit: 3,\n                currentCount: keyCount\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            fetchApiKeys();\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        configId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            if (apiKeys.length >= 0) {\n                fetchSubscriptionInfo();\n            }\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        apiKeys\n    ]);\n    const handleCreateApiKey = async (keyData)=>{\n        try {\n            setCreating(true);\n            const response = await fetch('/api/user-api-keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...keyData,\n                    custom_api_config_id: configId\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to create API key');\n            }\n            const newApiKey = await response.json();\n            // Show the full API key in a special dialog since it's only shown once\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('API key created successfully!');\n            // Add the new key to the list with proper structure\n            const newKeyForDisplay = {\n                ...newApiKey,\n                // Add any missing fields that might be needed for display\n                custom_api_configs: {\n                    id: configId,\n                    name: configName\n                },\n                // Ensure we have a masked_key for display\n                masked_key: newApiKey.api_key ? newApiKey.api_key.slice(-4) : 'xxxx'\n            };\n            setApiKeys((prevKeys)=>{\n                const newKeys = [\n                    newKeyForDisplay,\n                    ...prevKeys\n                ];\n                // Update subscription info with new count\n                fetchSubscriptionInfo(newKeys.length);\n                return newKeys;\n            });\n            // Don't refresh immediately to avoid closing the success dialog\n            // The user will see the new key in the list when they close the dialog\n            return newApiKey;\n        } catch (error) {\n            console.error('Error creating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(error.message || 'Failed to create API key');\n            throw error;\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleRevokeApiKey = async (keyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === keyId);\n        const keyName = (apiKey === null || apiKey === void 0 ? void 0 : apiKey.key_name) || 'this API key';\n        confirmation.showConfirmation({\n            title: 'Revoke API Key',\n            message: 'Are you sure you want to revoke \"'.concat(keyName, '\"? This action cannot be undone and will immediately disable the key.'),\n            confirmText: 'Revoke Key',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            try {\n                const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                    method: 'DELETE'\n                });\n                if (!response.ok) {\n                    const error = await response.json();\n                    throw new Error(error.error || 'Failed to revoke API key');\n                }\n                // Update the key status to revoked immediately after successful API call\n                setApiKeys((prevKeys)=>prevKeys.map((key)=>key.id === keyId ? {\n                            ...key,\n                            status: 'revoked'\n                        } : key));\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.success('API key revoked successfully');\n            } catch (error) {\n                console.error('Error revoking API key:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_3__.toast.error(error.message || 'Failed to revoke API key');\n                throw error; // Re-throw to let the confirmation modal handle the error state\n            }\n        });\n    };\n    const handleCreateDialogClose = (open)=>{\n        setShowCreateDialog(open);\n        // If dialog is being closed, refresh the API keys to ensure we have the latest data\n        if (!open) {\n            fetchApiKeys();\n        }\n    };\n    const canCreateMoreKeys = subscriptionInfo ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit : true;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin mr-2 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading API keys...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 214,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold flex items-center gap-2 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"API Keys\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mt-1\",\n                                children: [\n                                    \"Generate API keys for programmatic access to \",\n                                    configName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            \"Create API Key\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-end gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                disabled: true,\n                                className: \"flex items-center gap-2 opacity-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Create API Key\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-orange-400 font-medium\",\n                                children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan for more API keys' : 'API key limit reached - upgrade for unlimited keys'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement_FreeTierMarketingBanner__WEBPACK_IMPORTED_MODULE_6__.FreeTierMarketingBanner, {\n                message: \"Unlock intelligent routing and more API keys\",\n                variant: \"compact\",\n                className: \"mb-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement_FreeTierMarketingBanner__WEBPACK_IMPORTED_MODULE_6__.StarterTierHint, {\n                className: \"mb-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            apiKeys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2 text-white\",\n                            children: \"No API Keys\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-4\",\n                            children: \"Create your first API key to start using the RouKey API programmatically.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, this),\n                        canCreateMoreKeys ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setShowCreateDialog(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 17\n                                }, this),\n                                \"Create Your First API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    disabled: true,\n                                    className: \"opacity-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Create Your First API Key\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-orange-400 font-medium\",\n                                    children: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) === 'free' ? 'Upgrade to Starter plan to create API keys' : 'API key limit reached - upgrade for unlimited keys'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: apiKeys.map((apiKey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyCard__WEBPACK_IMPORTED_MODULE_4__.ApiKeyCard, {\n                        apiKey: apiKey,\n                        onRevoke: handleRevokeApiKey\n                    }, apiKey.id, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 307,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_5__.CreateApiKeyDialog, {\n                open: showCreateDialog,\n                onOpenChange: handleCreateDialogClose,\n                onCreateApiKey: handleCreateApiKey,\n                configName: configName,\n                creating: creating,\n                subscriptionTier: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) || 'starter'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyManager, \"wzx+xNoPucKu2oL+bFaGPsR3hPI=\", false, function() {\n    return [\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_8__.useConfirmation\n    ];\n});\n_c = ApiKeyManager;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\n"));

/***/ })

});