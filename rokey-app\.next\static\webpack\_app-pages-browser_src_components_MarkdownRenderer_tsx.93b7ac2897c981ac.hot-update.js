"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_MarkdownRenderer_tsx",{

/***/ "(app-pages-browser)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Prism!=!react-syntax-highlighter */ \"(app-pages-browser)/./node_modules/react-syntax-highlighter/dist/esm/prism.js\");\n/* harmony import */ var react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-syntax-highlighter/dist/esm/styles/prism */ \"(app-pages-browser)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/one-dark.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-visit */ \"(app-pages-browser)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Error Boundary component for handling markdown rendering errors\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError() {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch() {\n        this.props.onError();\n    }\n    render() {\n        if (this.state.hasError) {\n            return null; // Let parent component handle the error display\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n}\n// Custom remark plugin to prevent code blocks from being wrapped in paragraphs\nfunction remarkUnwrapCodeBlocks() {\n    return (tree)=>{\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_3__.visit)(tree, 'paragraph', (node, index, parent)=>{\n            if (node.children.length === 1 && node.children[0].type === 'code' && typeof index === 'number' && parent && parent.children) {\n                // Replace paragraph containing only a code block with the code block itself\n                parent.children[index] = node.children[0];\n            }\n        });\n    };\n}\nfunction MarkdownRenderer(param) {\n    let { content, className = '' } = param;\n    _s();\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarkdownRenderer.useEffect\": ()=>{\n            setHasError(false);\n        }\n    }[\"MarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    // Preprocess content to handle plain text with line breaks\n    const preprocessContent = (text)=>{\n        // If content already has markdown formatting (headers, lists, etc.), return as-is\n        if (text.includes('# ') || text.includes('## ') || text.includes('* ') || text.includes('- ') || text.includes('**') || text.includes('```')) {\n            return text;\n        }\n        // For plain text content, convert line breaks to proper markdown paragraphs\n        // Split by double line breaks first (already proper paragraphs)\n        const paragraphs = text.split(/\\n\\s*\\n/);\n        // Process each paragraph to handle single line breaks within paragraphs\n        const processedParagraphs = paragraphs.map((paragraph)=>{\n            // Trim whitespace\n            paragraph = paragraph.trim();\n            if (!paragraph) return '';\n            // If paragraph contains single line breaks, treat them as soft breaks\n            // Replace single line breaks with spaces, but preserve intentional breaks\n            return paragraph.replace(/\\n(?!\\n)/g, ' ');\n        });\n        // Join paragraphs with double line breaks for proper markdown formatting\n        return processedParagraphs.filter((p)=>p).join('\\n\\n');\n    };\n    const processedContent = preprocessContent(content);\n    if (hasError) {\n        // Fallback to simple text rendering if markdown fails\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"markdown-content \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"whitespace-pre-wrap text-sm text-white leading-relaxed\",\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n                onError: ()=>setHasError(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n                    remarkPlugins: [\n                        remark_gfm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                        remarkUnwrapCodeBlocks\n                    ],\n                    components: {\n                        // Headers\n                        h1: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h2: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h3: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h4: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Paragraphs\n                        p: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3 last:mb-0 leading-relaxed text-white break-words\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Bold and italic\n                        strong: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"font-bold text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        em: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                className: \"italic text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Lists\n                        ul: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside mb-3 space-y-1 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        ol: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"list-decimal list-inside mb-3 space-y-1 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        li: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"leading-relaxed text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Code blocks and inline code\n                        code: (param)=>{\n                            let { node, inline, className, children, ...props } = param;\n                            const match = /language-(\\w+)/.exec(className || '');\n                            const language = match ? match[1] : '';\n                            const codeContent = String(children).replace(/\\n$/, '');\n                            if (!inline) {\n                                // Check if this is a short single-line code snippet that should be treated as enhanced inline\n                                const isShortSnippet = codeContent.length <= 60 && !codeContent.includes('\\n') && !language;\n                                if (isShortSnippet) {\n                                    // Treat short snippets as enhanced inline code with subtle highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-orange-50 text-orange-700 px-1.5 py-0.5 rounded text-sm font-mono border border-orange-200\",\n                                        children: codeContent\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                                // Handle actual code blocks (both with and without language detection)\n                                if (language) {\n                                    // Code block with syntax highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                                language: language,\n                                                PreTag: \"div\",\n                                                className: \"text-sm\",\n                                                ...props,\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, void 0);\n                                } else {\n                                    // Multi-line code block without language (plain text code block)\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    children: codeContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: \"bg-gray-800 text-gray-100 px-1.5 py-0.5 rounded text-sm font-mono\",\n                                ...props,\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, void 0);\n                        },\n                        // Blockquotes\n                        blockquote: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-300\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Links\n                        a: (param)=>{\n                            let { children, href } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: href,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Tables\n                        table: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto my-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full border border-gray-600 rounded-lg\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        thead: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-800\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        tbody: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"divide-y divide-gray-600 bg-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        tr: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-800\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        th: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-3 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider border-b border-gray-600\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        td: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-3 py-2 text-sm text-white border-b border-gray-600\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Horizontal rule\n                        hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-4 border-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, void 0)\n                    },\n                    children: processedContent\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n_s(MarkdownRenderer, \"Y10BwSn8jQ4wQtRAtRxoscEk2KA=\");\n_c = MarkdownRenderer;\nvar _c;\n$RefreshReg$(_c, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MarkdownRenderer.tsx\n"));

/***/ })

});