{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}}, "functions": {"/api/external/v1/api-keys/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/api-keys/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/api-keys/route.js"], "name": "app/api/external/v1/api-keys/route", "page": "/api/external/v1/api-keys/route", "matchers": [{"regexp": "^/api/external/v1/api\\-keys$", "originalSource": "/api/external/v1/api-keys"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}, "/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/833.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}, "/api/external/v1/api-keys/[keyId]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/api-keys/[keyId]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/api-keys/[keyId]/route.js"], "name": "app/api/external/v1/api-keys/[keyId]/route", "page": "/api/external/v1/api-keys/[keyId]/route", "matchers": [{"regexp": "^/api/external/v1/api\\-keys/(?<keyId>[^/]+?)$", "originalSource": "/api/external/v1/api-keys/[keyId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}, "/api/external/v1/configs/[configId]/keys/[keyId]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/keys/[keyId]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/keys/[keyId]/route.js"], "name": "app/api/external/v1/configs/[configId]/keys/[keyId]/route", "page": "/api/external/v1/configs/[configId]/keys/[keyId]/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)/keys/(?<keyId>[^/]+?)$", "originalSource": "/api/external/v1/configs/[configId]/keys/[keyId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}, "/api/external/v1/configs/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/route.js"], "name": "app/api/external/v1/configs/route", "page": "/api/external/v1/configs/route", "matchers": [{"regexp": "^/api/external/v1/configs$", "originalSource": "/api/external/v1/configs"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}, "/api/external/v1/docs/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/docs/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/app/api/external/v1/docs/route.js"], "name": "app/api/external/v1/docs/route", "page": "/api/external/v1/docs/route", "matchers": [{"regexp": "^/api/external/v1/docs$", "originalSource": "/api/external/v1/docs"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}, "/api/external/v1/configs/[configId]/routing/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/routing/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/routing/route.js"], "name": "app/api/external/v1/configs/[configId]/routing/route", "page": "/api/external/v1/configs/[configId]/routing/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)/routing$", "originalSource": "/api/external/v1/configs/[configId]/routing"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}, "/api/external/v1/usage/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/usage/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/usage/route.js"], "name": "app/api/external/v1/usage/route", "page": "/api/external/v1/usage/route", "matchers": [{"regexp": "^/api/external/v1/usage$", "originalSource": "/api/external/v1/usage"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}, "/api/external/v1/models/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/models/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/models/route.js"], "name": "app/api/external/v1/models/route", "page": "/api/external/v1/models/route", "matchers": [{"regexp": "^/api/external/v1/models$", "originalSource": "/api/external/v1/models"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}, "/api/external/v1/providers/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/providers/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/833.js", "server/app/api/external/v1/providers/route.js"], "name": "app/api/external/v1/providers/route", "page": "/api/external/v1/providers/route", "matchers": [{"regexp": "^/api/external/v1/providers$", "originalSource": "/api/external/v1/providers"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}, "/api/external/v1/configs/[configId]/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/route.js"], "name": "app/api/external/v1/configs/[configId]/route", "page": "/api/external/v1/configs/[configId]/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)$", "originalSource": "/api/external/v1/configs/[configId]"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}, "/api/external/v1/configs/[configId]/keys/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/configs/[configId]/keys/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/edge-chunks/580.js", "server/edge-chunks/918.js", "server/edge-chunks/109.js", "server/edge-chunks/44.js", "server/edge-chunks/833.js", "server/app/api/external/v1/configs/[configId]/keys/route.js"], "name": "app/api/external/v1/configs/[configId]/keys/route", "page": "/api/external/v1/configs/[configId]/keys/route", "matchers": [{"regexp": "^/api/external/v1/configs/(?<configId>[^/]+?)/keys$", "originalSource": "/api/external/v1/configs/[configId]/keys"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yL-uQebLHR_wQdlJDsJLY", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "3cd333fc8b475787f91ec5cd51ade493", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "010e73b1f35f34319d337db2c52734fbd5f255938a3a9c392672e5ad28750361", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be2fd28c3c08338b0d41584722aa65a8b8ce42ed93cbd3971fc39d0dea0675ea"}}}, "sortedMiddleware": ["/"]}