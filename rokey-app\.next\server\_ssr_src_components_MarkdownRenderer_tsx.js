"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_MarkdownRenderer_tsx";
exports.ids = ["_ssr_src_components_MarkdownRenderer_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Prism!=!react-syntax-highlighter */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/prism.js\");\n/* harmony import */ var react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-syntax-highlighter/dist/esm/styles/prism */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/one-dark.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CopyButton */ \"(ssr)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Error Boundary component for handling markdown rendering errors\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError() {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch() {\n        this.props.onError();\n    }\n    render() {\n        if (this.state.hasError) {\n            return null; // Let parent component handle the error display\n        }\n        return this.props.children;\n    }\n}\n// Custom remark plugin to prevent code blocks from being wrapped in paragraphs\nfunction remarkUnwrapCodeBlocks() {\n    return (tree)=>{\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_3__.visit)(tree, 'paragraph', (node, index, parent)=>{\n            if (node.children.length === 1 && node.children[0].type === 'code' && typeof index === 'number' && parent && parent.children) {\n                // Replace paragraph containing only a code block with the code block itself\n                parent.children[index] = node.children[0];\n            }\n        });\n    };\n}\n// Memoized MarkdownRenderer for better performance\nconst MarkdownRenderer = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(function MarkdownRenderer({ content, className = '' }) {\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarkdownRenderer.MarkdownRenderer.useEffect\": ()=>{\n            setHasError(false);\n        }\n    }[\"MarkdownRenderer.MarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    // Memoized preprocessing to avoid recalculation on every render\n    const processedContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"MarkdownRenderer.MarkdownRenderer.useMemo[processedContent]\": ()=>{\n            // Preprocess content to handle plain text with line breaks\n            const preprocessContent = {\n                \"MarkdownRenderer.MarkdownRenderer.useMemo[processedContent].preprocessContent\": (text)=>{\n                    // If content already has markdown formatting (headers, lists, etc.), return as-is\n                    if (text.includes('# ') || text.includes('## ') || text.includes('* ') || text.includes('- ') || text.includes('**') || text.includes('```')) {\n                        return text;\n                    }\n                    // For plain text content, convert line breaks to proper markdown paragraphs\n                    // Split by double line breaks first (already proper paragraphs)\n                    const paragraphs = text.split(/\\n\\s*\\n/);\n                    // Process each paragraph to handle single line breaks within paragraphs\n                    const processedParagraphs = paragraphs.map({\n                        \"MarkdownRenderer.MarkdownRenderer.useMemo[processedContent].preprocessContent.processedParagraphs\": (paragraph)=>{\n                            // Trim whitespace\n                            paragraph = paragraph.trim();\n                            if (!paragraph) return '';\n                            // If paragraph contains single line breaks, treat them as soft breaks\n                            // Replace single line breaks with spaces, but preserve intentional breaks\n                            return paragraph.replace(/\\n(?!\\n)/g, ' ');\n                        }\n                    }[\"MarkdownRenderer.MarkdownRenderer.useMemo[processedContent].preprocessContent.processedParagraphs\"]);\n                    // Join paragraphs with double line breaks for proper markdown formatting\n                    return processedParagraphs.filter({\n                        \"MarkdownRenderer.MarkdownRenderer.useMemo[processedContent].preprocessContent\": (p)=>p\n                    }[\"MarkdownRenderer.MarkdownRenderer.useMemo[processedContent].preprocessContent\"]).join('\\n\\n');\n                }\n            }[\"MarkdownRenderer.MarkdownRenderer.useMemo[processedContent].preprocessContent\"];\n            return preprocessContent(content);\n        }\n    }[\"MarkdownRenderer.MarkdownRenderer.useMemo[processedContent]\"], [\n        content\n    ]);\n    if (hasError) {\n        // Fallback to simple text rendering if markdown fails\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `markdown-content ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"whitespace-pre-wrap text-sm text-white leading-relaxed\",\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this);\n    }\n    // Memoized component configuration to avoid recreation on every render\n    const markdownComponents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ()=>({\n                // Headers\n                h1: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-white\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                h2: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-white\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                h3: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-white\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                h4: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-white\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                // Paragraphs\n                p: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-3 last:mb-0 leading-relaxed text-white break-words\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                // Bold and italic\n                strong: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            className: \"font-bold text-white\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                em: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                            className: \"italic text-white\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                // Lists\n                ul: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"list-disc list-inside mb-3 space-y-1 text-white\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                ol: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal list-inside mb-3 space-y-1 text-white\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                li: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"leading-relaxed text-white\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                // Code blocks and inline code\n                code: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ node, inline, className, children, ...props })=>{\n                        const match = /language-(\\w+)/.exec(className || '');\n                        const language = match ? match[1] : '';\n                        const codeContent = String(children).replace(/\\n$/, '');\n                        if (!inline) {\n                            // Check if this is a short single-line code snippet that should be treated as enhanced inline\n                            const isShortSnippet = codeContent.length <= 60 && !codeContent.includes('\\n') && !language;\n                            if (isShortSnippet) {\n                                // Treat short snippets as enhanced inline code with subtle highlighting\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: \"bg-orange-50 text-orange-700 px-1.5 py-0.5 rounded text-sm font-mono border border-orange-200\",\n                                    children: codeContent\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this);\n                            }\n                            // Handle actual code blocks (both with and without language detection)\n                            if (language) {\n                                // Code block with syntax highlighting\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                text: codeContent,\n                                                variant: \"code\",\n                                                size: \"sm\",\n                                                title: \"Copy code\",\n                                                className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                            language: language,\n                                            PreTag: \"div\",\n                                            className: \"text-sm\",\n                                            ...props,\n                                            children: codeContent\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, this);\n                            } else {\n                                // Multi-line code block without language (plain text code block)\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                text: codeContent,\n                                                variant: \"code\",\n                                                size: \"sm\",\n                                                title: \"Copy code\",\n                                                className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 17\n                                }, this);\n                            }\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-gray-800 text-gray-100 px-1.5 py-0.5 rounded text-sm font-mono\",\n                            ...props,\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this);\n                    }\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                // Blockquotes\n                blockquote: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                            className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-300\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                // Links\n                a: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: href,\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                // Tables\n                table: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto my-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full border border-gray-600 rounded-lg\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                thead: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-800\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                tbody: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"divide-y divide-gray-600 bg-gray-900\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                tr: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: \"hover:bg-gray-800\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                th: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                            className: \"px-3 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider border-b border-gray-600\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                td: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                            className: \"px-3 py-2 text-sm text-white border-b border-gray-600\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"],\n                // Horizontal rule\n                hr: ({\n                    \"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\": ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                            className: \"my-4 border-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this)\n                })[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"]\n            })\n    }[\"MarkdownRenderer.MarkdownRenderer.useMemo[markdownComponents]\"], []); // Empty dependency array since components don't change\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `markdown-content ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 304,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n                onError: ()=>setHasError(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_6__.Markdown, {\n                    remarkPlugins: [\n                        remark_gfm__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                        remarkUnwrapCodeBlocks\n                    ],\n                    components: markdownComponents,\n                    children: processedContent\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 310,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 303,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MarkdownRenderer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MarkdownRenderer.tsx\n");

/***/ })

};
;