'use client';

import React, { lazy, Suspense, memo } from 'react';

// Lazy load the heavy markdown component
const MarkdownRenderer = lazy(() => import('./MarkdownRenderer'));

interface LazyMarkdownRendererProps {
  content: string;
  className?: string;
}

// Loading skeleton for markdown content
const MarkdownSkeleton = () => (
  <div className="space-y-2 animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
    <div className="h-4 bg-gray-200 rounded w-5/6"></div>
  </div>
);

// Memoized component to prevent unnecessary re-renders
const LazyMarkdownRenderer = memo(function LazyMarkdownRenderer({ content, className = '' }: LazyMarkdownRendererProps) {
  return (
    <Suspense fallback={<MarkdownSkeleton />}>
      <MarkdownRenderer content={content} className={className} />
    </Suspense>
  );
});

export default LazyMarkdownRenderer;
