"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx":
/*!***********************************************************!*\
  !*** ./src/components/UserApiKeys/CreateApiKeyDialog.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateApiKeyDialog: () => (/* binding */ CreateApiKeyDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Eye_EyeOff_Key_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Eye,EyeOff,Key!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Eye_EyeOff_Key_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Eye,EyeOff,Key!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Eye_EyeOff_Key_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Eye,EyeOff,Key!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Eye_EyeOff_Key_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Eye,EyeOff,Key!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Copy_Eye_EyeOff_Key_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Copy,Eye,EyeOff,Key!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ CreateApiKeyDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction CreateApiKeyDialog(param) {\n    let { open, onOpenChange, onCreateApiKey, configName, creating, subscriptionTier } = param;\n    _s();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('form');\n    const [createdApiKey, setCreatedApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFullKey, setShowFullKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true); // Show full key by default in success step\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        key_name: '',\n        expires_at: ''\n    });\n    // Reset dialog state when it's closed and reopened\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CreateApiKeyDialog.useEffect\": ()=>{\n            if (!open) {\n                // Reset state when dialog is closed\n                setStep('form');\n                setCreatedApiKey(null);\n                setShowFullKey(true);\n                setCopied(false);\n                setFormData({\n                    key_name: '',\n                    expires_at: ''\n                });\n            }\n        }\n    }[\"CreateApiKeyDialog.useEffect\"], [\n        open\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.key_name.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Please enter a name for your API key');\n            return;\n        }\n        try {\n            const result = await onCreateApiKey({\n                key_name: formData.key_name.trim(),\n                expires_at: formData.expires_at || undefined\n            });\n            console.log('[CreateApiKeyDialog] API key creation result:', result);\n            setCreatedApiKey(result);\n            setStep('success');\n            console.log('[CreateApiKeyDialog] Switched to success step');\n        } catch (error) {\n            console.error('[CreateApiKeyDialog] Error creating API key:', error);\n        // Error is handled in the parent component\n        }\n    };\n    const copyApiKey = async ()=>{\n        if (createdApiKey === null || createdApiKey === void 0 ? void 0 : createdApiKey.api_key) {\n            try {\n                await navigator.clipboard.writeText(createdApiKey.api_key);\n                setCopied(true);\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success('API key copied to clipboard');\n                setTimeout(()=>setCopied(false), 2000);\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to copy API key');\n            }\n        }\n    };\n    const handleClose = ()=>{\n        // Only allow closing if we're in the form step or if user explicitly closes from success step\n        if (step === 'form') {\n            // Reset all state\n            setStep('form');\n            setCreatedApiKey(null);\n            setShowFullKey(true);\n            setFormData({\n                key_name: '',\n                expires_at: ''\n            });\n            // Close the dialog\n            onOpenChange(false);\n        }\n    };\n    const handleSuccessClose = ()=>{\n        // Reset all state\n        setStep('form');\n        setCreatedApiKey(null);\n        setShowFullKey(true);\n        setCopied(false);\n        setFormData({\n            key_name: '',\n            expires_at: ''\n        });\n        // Close the dialog\n        onOpenChange(false);\n    };\n    if (step === 'success' && createdApiKey) {\n        var _createdApiKey_api_key;\n        console.log('[CreateApiKeyDialog] Rendering success step with API key:', ((_createdApiKey_api_key = createdApiKey.api_key) === null || _createdApiKey_api_key === void 0 ? void 0 : _createdApiKey_api_key.substring(0, 20)) + '...');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n            open: open,\n            onOpenChange: ()=>{},\n            modal: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n                className: \"max-w-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                        className: \"text-center space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Eye_EyeOff_Key_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"API Key Created Successfully!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                                className: \"text-gray-600\",\n                                children: \"Save your API key now - this is the only time you'll see it in full.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6 py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                                className: \"border-red-200 bg-red-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Eye_EyeOff_Key_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                        className: \"text-red-800 font-medium\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Important:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" This is the only time you'll see the full API key. Make sure to copy and store it securely.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"Your API Key\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"flex-1 text-sm font-mono text-gray-900 break-all select-all\",\n                                                    children: showFullKey ? createdApiKey.api_key : \"\".concat(createdApiKey.key_prefix, \"_\").concat('*'.repeat(28)).concat(createdApiKey.api_key.slice(-4))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>setShowFullKey(!showFullKey),\n                                                            className: \"h-8 w-8 p-0\",\n                                                            title: showFullKey ? \"Hide key\" : \"Show key\",\n                                                            children: showFullKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Eye_EyeOff_Key_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 38\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Eye_EyeOff_Key_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 71\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: copyApiKey,\n                                                            className: \"h-8 w-8 p-0 \".concat(copied ? 'text-green-600' : ''),\n                                                            title: \"Copy to clipboard\",\n                                                            children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 33\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Eye_EyeOff_Key_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 70\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: copyApiKey,\n                                        variant: \"outline\",\n                                        className: \"w-full\",\n                                        disabled: copied,\n                                        children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 mr-2\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Copied!\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Eye_EyeOff_Key_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Copy API Key\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 text-sm bg-gray-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                className: \"text-gray-600\",\n                                                children: \"Key Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: createdApiKey.key_name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                className: \"text-gray-600\",\n                                                children: \"Created\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: new Date(createdApiKey.created_at).toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: handleSuccessClose,\n                            className: \"w-full\",\n                            children: \"I've Saved My API Key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: handleClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Copy_Eye_EyeOff_Key_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                \"Create API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: [\n                                \"Create a new API key for programmatic access to \",\n                                configName\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"key_name\",\n                                        children: \"API Key Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        id: \"key_name\",\n                                        value: formData.key_name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    key_name: e.target.value\n                                                })),\n                                        placeholder: \"e.g., Production API Key\",\n                                        required: true,\n                                        className: \"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: \"A descriptive name to help you identify this API key\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"expires_at\",\n                                        children: \"Expiration Date (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        id: \"expires_at\",\n                                        type: \"datetime-local\",\n                                        value: formData.expires_at,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    expires_at: e.target.value\n                                                })),\n                                        min: new Date().toISOString().slice(0, 16),\n                                        className: \"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: \"Leave empty for no expiration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: handleClose,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    type: \"submit\",\n                                    disabled: creating,\n                                    children: creating ? 'Creating...' : 'Create API Key'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n            lineNumber: 248,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateApiKeyDialog, \"hq5o3ywepfZAIyKbaOSJm9osQ9g=\");\n_c = CreateApiKeyDialog;\nvar _c;\n$RefreshReg$(_c, \"CreateApiKeyDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\n"));

/***/ })

});